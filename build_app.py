#!/usr/bin/env python3
"""
打包脚本 - 将Google Play API工具打包成独立可执行文件
"""
import os
import subprocess
import sys
import shutil

def build_executable():
    print("🚀 开始打包Google Play API工具...")
    
    # 检查必要文件
    required_files = ['web_gui.py', 'apis.py', 'jwt_generator.py', 'cache.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 创建打包目录
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # PyInstaller命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # 打包成单个文件
        '--windowed',  # 无控制台窗口（可选）
        '--name=GooglePlayAPITool',  # 可执行文件名
        '--add-data=apis.py:.',  # 包含依赖文件
        '--add-data=jwt_generator.py:.',
        '--add-data=cache.py:.',
        '--hidden-import=jwt',
        '--hidden-import=requests',
        '--hidden-import=cryptography',
        'web_gui.py'
    ]
    
    print("📦 执行打包命令...")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        
        # 检查生成的文件
        exe_path = os.path.join('dist', 'GooglePlayAPITool')
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 可执行文件位置: {exe_path}")
            print(f"📏 文件大小: {size:.1f} MB")
            
            # 创建使用说明
            create_readme()
            
            print("\n🎉 打包完成!")
            print("📋 分发说明:")
            print("1. 将 dist/GooglePlayAPITool 文件发送给用户")
            print("2. 用户需要准备自己的 googleapi.json 文件")
            print("3. 双击运行，浏览器会自动打开界面")
            
        else:
            print("❌ 未找到生成的可执行文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    return True

def create_readme():
    """创建使用说明文件"""
    readme_content = """# Google Play Developer API Tool

## 使用说明

### 1. 准备工作
- 确保你有 Google Play Developer API 的 JSON 凭证文件（通常命名为 googleapi.json）
- 将该文件放在与可执行文件相同的目录下

### 2. 运行程序
- 双击 `GooglePlayAPITool` 可执行文件
- 程序会自动启动本地服务器并打开浏览器
- 如果浏览器没有自动打开，请手动访问 http://localhost:8080

### 3. 使用界面
1. **输入信息**：
   - Package Name: 应用包名（如：com.example.app）
   - Order ID: Google Play 订单ID（如：GPA.1234-5678-9012-34567）

2. **执行操作**：
   - 点击 "1. Get Order Info" 获取订单信息
   - 点击 "2. Consume Product" 消费产品
   - 点击 "3. Product Details" 获取产品详情
   - 或者点击 "Run All Steps" 一次性执行所有步骤

3. **查看结果**：
   - 所有操作结果会显示在 "Output" 区域
   - 输入的信息会自动保存，下次打开时会恢复

### 4. 停止程序
- 关闭浏览器标签页
- 在终端中按 Ctrl+C 停止服务器

### 5. 故障排除
- 如果端口 8080 被占用，程序可能无法启动
- 确保 googleapi.json 文件格式正确且有效
- 检查网络连接是否正常

### 技术支持
如有问题，请检查控制台输出的错误信息。
"""
    
    with open('dist/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("📝 已创建使用说明文件: dist/README.md")

if __name__ == "__main__":
    success = build_executable()
    if not success:
        sys.exit(1)
