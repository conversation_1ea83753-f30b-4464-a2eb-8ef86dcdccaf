@echo off
echo 🚀 启动 Google Play API Tool...
echo 📱 请确保 googleapi.json 文件在当前目录
echo 🌐 浏览器将自动打开 http://localhost:8080
echo.

REM 显示当前目录内容用于调试
echo 📂 当前目录内容:
dir /b
echo.

REM 检查配置文件
if not exist "googleapi.json" (
    echo ⚠️  警告: 未找到 googleapi.json 文件
    echo 请将您的 Google API 凭证文件重命名为 googleapi.json 并放在此目录
    echo.
)

REM 启动应用
if exist "GooglePlayAPITool.exe" (
    GooglePlayAPITool.exe
) else if exist "GooglePlayAPITool" (
    GooglePlayAPITool
) else (
    echo ❌ 错误: 未找到可执行文件 GooglePlayAPITool.exe 或 GooglePlayAPITool
    echo 请确保可执行文件在当前目录中
    echo.
    pause
    exit /b 1
)

echo.
echo 👋 应用已退出
pause
