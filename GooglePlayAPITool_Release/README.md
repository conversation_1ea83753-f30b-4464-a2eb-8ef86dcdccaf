# Google Play Developer API Tool

**版本**: 1.0  
**构建时间**: 2025-08-12 11:31:54  
**平台**: macOS / Linux

## 📋 功能介绍

这是一个用于管理 Google Play Developer API 的工具，主要功能包括：

- 🔍 **获取订单信息** - 查询 Google Play 内购订单详情
- 🛒 **消费产品** - 标记内购产品为已消费状态  
- 📊 **产品详情** - 获取内购产品的详细信息
- 🚀 **一键执行** - 自动完成所有操作流程

## 🚀 快速开始

### 1. 准备 Google API 凭证

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建或选择项目
3. 启用 Google Play Developer API
4. 创建服务账号并下载 JSON 凭证文件
5. 将凭证文件重命名为 `googleapi.json` 并放在本工具目录下

### 2. 运行程序

**方法一：使用启动脚本（推荐）**
```bash
# macOS/Linux
./start.sh

# Windows
start.bat
```

**方法二：直接运行**
```bash
# 交互模式（默认）
./GooglePlayAPITool

# 自动模式（无需用户交互）
./GooglePlayAPITool --auto
```

**🔧 智能端口处理**
- 默认使用端口 8080
- 如果端口被占用，程序会：
  1. 🔍 自动检测占用进程信息
  2. 💡 提供处理选项：结束进程、使用其他端口或退出
  3. 🔄 自动寻找可用端口（8081-8099, 9000-9019）
- 自动模式（`--auto`）下会直接使用其他可用端口，无需用户交互

### 3. 使用界面

1. 程序启动后会自动打开浏览器访问 http://localhost:8080
2. 在界面中输入：
   - **Package Name**: 应用包名（如：com.example.app）
   - **Order ID**: Google Play 订单ID（如：GPA.1234-5678-9012-34567）
3. 选择操作：
   - 单步执行：按顺序点击 1→2→3
   - 一键执行：点击 "Run All Steps"

## 📁 文件说明

```
GooglePlayAPITool_Release/
├── GooglePlayAPITool          # 主程序（可执行文件）
├── start.sh                   # macOS/Linux 启动脚本
├── start.bat                  # Windows 启动脚本  
├── googleapi_sample.json      # 配置文件示例
├── README.md                  # 本说明文件
└── googleapi.json            # 您的 API 凭证文件（需要您提供）
```

## 🔧 故障排除

### 常见问题

**Q: 浏览器没有自动打开？**  
A: 手动访问 http://localhost:8080

**Q: 提示端口被占用？**
A: 程序已内置智能端口处理功能：
   - 自动检测端口占用情况并显示占用进程
   - 提供选项结束占用进程或使用其他端口
   - 自动寻找可用端口（8081-8099, 9000-9019）
   - 使用 `--auto` 参数可启用自动模式

**Q: API 调用失败？**  
A: 检查 googleapi.json 文件是否正确，确认 API 权限已正确配置

**Q: 程序无法启动？**  
A: 确认系统权限，在 macOS 上可能需要在"安全性与隐私"中允许运行

### 日志查看

程序运行时的详细日志会显示在：
- 启动脚本的控制台窗口
- 浏览器界面的 Output 区域

## 🔒 安全说明

- `googleapi.json` 文件包含敏感信息，请妥善保管
- 不要将凭证文件分享给他人
- 建议定期轮换 API 密钥

## 📞 技术支持

如遇到问题，请检查：
1. Google API 凭证是否有效
2. 网络连接是否正常
3. 控制台错误信息

## 📄 许可证

本工具仅供学习和合法商业用途使用。
