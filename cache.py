import os
import json
import time

TOKEN_CACHE_FILE = "google_token_cache.json"

def load_cached_token():
    """读取本地缓存的 token"""
    if os.path.exists(TOKEN_CACHE_FILE):
        with open(TOKEN_CACHE_FILE, "r") as f:
            cache = json.load(f)
            if cache["expires_at"] > time.time():
                return cache["access_token"]
    return None

def save_token_to_cache(token, expires_in):
    """保存 token 到本地文件"""
    cache = {
        "access_token": token,
        "expires_at": time.time() + expires_in - 60  # 提前 1 分钟过期
    }
    with open(TOKEN_CACHE_FILE, "w") as f:
        json.dump(cache, f)