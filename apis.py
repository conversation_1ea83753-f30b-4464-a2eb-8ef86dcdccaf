from itertools import product

from jwt_generator import J<PERSON>TGenerator
import cache
import requests
import os


class GooglePlayAPI:
    """Google Play Developer API 客户端"""

    def __init__(self):
        self.jwt_generator = JWTGenerator()
        self.base_url = "https://androidpublisher.googleapis.com/androidpublisher/v3/applications"
        self.package_name = None
        self.product_id = None
        self.purchase_token = None
        self.purchase_order_id = None
        self._access_token = None

    def _ensure_jwt_token(self):
        """确保JWT Token已生成"""
        if not self.jwt_generator.jwt_token:
            # 尝试使用上次保存的文件
            last_file = self.jwt_generator.get_last_file_path()
            if last_file and os.path.exists(last_file):
                self.jwt_generator.load_google_api_config(last_file)
                self.jwt_generator.generate_jwt_token()
            else:
                raise ValueError("请先生成JWT Token。使用 jwt_generator.py 或通过GUI选择Google API文件。")

    def _get_google_apis_token(self):
        """获取Google APIs访问令牌"""
        # 检查缓存的token
        cached_token = cache.load_cached_token()
        if cached_token:
            return cached_token

        # 确保有JWT token
        self._ensure_jwt_token()

        token_url = "https://oauth2.googleapis.com/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        body = {
            "grant_type": "urn:ietf:params:oauth:grant-type:jwt-bearer",
            "assertion": self.jwt_generator.jwt_token,
        }

        response = requests.post(token_url, headers=headers, data=body)

        if response.status_code != 200:
            raise Exception(f"获取访问令牌失败: {response.status_code} - {response.text}")

        # 获取访问令牌
        access_token = response.json()["access_token"]
        cache.save_token_to_cache(access_token, expires_in=60*60)  # 一小时过期

        return access_token

    def _get_access_token(self):
        """获取访问令牌（带缓存）"""
        if not self._access_token:
            self._access_token = self._get_google_apis_token()
        return self._access_token

    def _generate_headers(self):
        """生成请求头"""
        return {
            "Authorization": f"Bearer {self._get_access_token()}",
            "Accept": "application/json"
        }

    def get_order(self, body):
        """获取订单信息"""
        self.package_name = body["package_name"]
        self.purchase_order_id = body["order_id"]

        url = f"{self.base_url}/{self.package_name}/orders/{self.purchase_order_id}"
        response = requests.get(url, headers=self._generate_headers())

        if response.status_code != 200:
            raise Exception(f"获取订单信息失败: {response.status_code} - {response.text}")

        json_response = response.json()
        print(f"订单信息获取成功: {json_response}")

        self.product_id = json_response["lineItems"][0]["productId"]
        self.purchase_token = json_response["purchaseToken"]

        return json_response

    def product_consume(self):
        """消费产品"""
        if not self.package_name or not self.product_id or not self.purchase_token:
            raise ValueError("请先调用 get_order() 获取订单信息")

        # 请求 URL
        url = f"{self.base_url}/{self.package_name}/purchases/products/{self.product_id}/tokens/{self.purchase_token}:consume"

        # 发起 POST 请求
        response = requests.post(url, headers=self._generate_headers())

        # 解析结果
        if response.status_code == 204:
            print("消费成功")
            return True
        else:
            print("该订单消费失败，可能是由于已经被消费了")
            print(f"错误详情: {response.status_code} - {response.text}")
            return False

    def product_details(self):
        """获取产品详情"""
        if not self.package_name or not self.product_id or not self.purchase_token:
            raise ValueError("请先调用 get_order() 获取订单信息")

        # 请求url
        url = f"{self.base_url}/{self.package_name}/purchases/products/{self.product_id}/tokens/{self.purchase_token}"
        response = requests.get(url, headers=self._generate_headers())

        if response.status_code == 200:
            print(f"内购详情: \n{response.text}")
            return response.json()
        else:
            error_msg = f"获取内购详情失败: code-{response.status_code} - {response.text}"
            print(error_msg)
            raise Exception(error_msg)


# 全局API实例（向后兼容）
_api_instance = GooglePlayAPI()

# 向后兼容的函数接口
def get_order(body):
    """获取订单信息 - 向后兼容函数"""
    return _api_instance.get_order(body)

def product_consume():
    """消费产品 - 向后兼容函数"""
    return _api_instance.product_consume()

def product_details():
    """获取产品详情 - 向后兼容函数"""
    return _api_instance.product_details()

def generate_headers():
    """生成请求头 - 向后兼容函数"""
    return _api_instance._generate_headers()

# 便捷函数：设置JWT生成器
def set_jwt_generator(jwt_generator):
    """设置JWT生成器实例"""
    _api_instance.jwt_generator = jwt_generator

def set_google_api_file(file_path):
    """设置Google API文件路径"""
    _api_instance.jwt_generator.load_google_api_config(file_path)
    _api_instance.jwt_generator.generate_jwt_token()
    # 清除缓存的访问令牌，强制重新获取
    _api_instance._access_token = None