#!/usr/bin/env python3
import http.server
import socketserver
import json
import threading
import webbrowser
import os
import sys
import socket
import subprocess
import platform
import time
from datetime import datetime
from apis import get_order, product_consume, product_details, set_jwt_generator
from file_selector import WebFileSelector
from jwt_generator import JWTGenerator

# 全局变量存储日志
captured_logs = []
log_lock = threading.Lock()

# 全局文件选择器
file_selector = WebFileSelector()
jwt_generator = JWTGenerator()

# 设置API使用的JWT生成器
set_jwt_generator(jwt_generator)

def add_log(message):
    """添加日志消息"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    with log_lock:
        captured_logs.append(f"[{timestamp}] {message}")

class LogCapture:
    """捕获标准输出和错误输出"""
    def write(self, text):
        if text.strip():  # 忽略空行
            add_log(text.strip())
    
    def flush(self):
        pass

# 创建日志捕获器
log_capture = LogCapture()

class APIHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            html_content = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Play Developer API Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        button { background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .run-all { background-color: #2196F3; font-size: 16px; font-weight: bold; }
        .run-all:hover { background-color: #1976D2; }
        .output { background-color: #f8f8f8; border: 1px solid #ddd; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; white-space: pre-wrap; }
        .button-group { text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Play Developer API Tool</h1>
        
        <div class="form-group">
            <label for="package_name">Package Name:</label>
            <input type="text" id="package_name" placeholder="e.g., com.tf.localuv">
        </div>
        
        <div class="form-group">
            <label for="order_id">Order ID:</label>
            <input type="text" id="order_id" placeholder="e.g., GPA.3314-3666-3496-87495">
        </div>
        
        <div class="form-group">
            <label for="api_file">Google API JSON File:</label>
            <input type="file" id="api_file" accept=".json" onchange="handleFileSelection()">
            <div id="file_status" style="margin-top: 5px; font-size: 12px; padding: 5px; border-radius: 3px;"></div>
            <div id="file_details" style="margin-top: 5px; font-size: 11px; color: #666;"></div>
        </div>
        
        <div class="button-group">
            <button id="jwt_btn" onclick="generateJWT()" disabled>Generate JWT Token</button>
        </div>

        <div class="button-group">
            <button id="order_btn" onclick="getOrderInfo()" disabled>1. Get Order Info</button>
            <button id="consume_btn" onclick="consumeProduct()" disabled>2. Consume Product</button>
            <button id="details_btn" onclick="productDetails()" disabled>3. Product Details</button>
        </div>

        <div class="button-group">
            <button class="run-all" onclick="runAllSteps()">Run All Steps</button>
        </div>
        
        <div class="form-group">
            <label for="output">Output:</label>
            <div id="output" class="output"></div>
        </div>
        
        <div class="button-group">
            <button onclick="clearOutput()">Clear Output</button>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedFile = null;
        let fileContent = null;
        let isFileValid = false;

        // 基础函数
        function logOutput(message) {
            const output = document.getElementById('output');
            output.textContent += message + '\\n';
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        // 数据保存
        function saveData() {
            const data = {
                package_name: document.getElementById('package_name').value,
                order_id: document.getElementById('order_id').value
            };
            localStorage.setItem('api_tool_data', JSON.stringify(data));
        }
        
        // 网络请求
        async function makeRequest(endpoint, data) {
            try {
                logOutput('⏳ 正在处理请求...');

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data || {})
                });

                // 检查HTTP状态
                if (!response.ok) {
                    const errorText = await response.text();
                    logOutput(`❌ HTTP错误 ${response.status}: ${response.statusText}`);
                    logOutput(`错误详情: ${errorText}`);
                    return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
                }

                // 尝试解析JSON
                let result;
                try {
                    const responseText = await response.text();
                    if (!responseText.trim()) {
                        return { success: false, error: '服务器返回空响应' };
                    }
                    result = JSON.parse(responseText);
                } catch (jsonError) {
                    logOutput('❌ JSON解析错误: ' + jsonError.message);
                    try {
                        const errorText = responseText || '(空响应)';
                        logOutput('服务器响应: ' + errorText.substring(0, 200) + (errorText.length > 200 ? '...' : ''));
                    } catch (e) {
                        logOutput('服务器响应: (无法读取响应内容)');
                    }
                    return { success: false, error: 'JSON解析错误: ' + jsonError.message };
                }

                // 显示服务器返回的日志
                if (result && result.logs && Array.isArray(result.logs) && result.logs.length > 0) {
                    result.logs.forEach(log => {
                        if (log && typeof log === 'string') {
                            logOutput(log);
                        }
                    });
                }

                return result;
            } catch (error) {
                logOutput('❌ 网络错误: ' + error.message);
                return { success: false, error: error.message };
            }
        }
        
        // 文件处理函数
        async function handleFileSelection() {
            const fileInput = document.getElementById('api_file');
            const fileStatus = document.getElementById('file_status');
            const fileDetails = document.getElementById('file_details');
            const jwtBtn = document.getElementById('jwt_btn');
            const orderBtn = document.getElementById('order_btn');

            // 重置状态
            selectedFile = null;
            fileContent = null;
            isFileValid = false;
            updateButtonStates();

            if (fileInput.files.length === 0) {
                fileStatus.textContent = '';
                fileStatus.style.backgroundColor = '';
                fileDetails.textContent = '';
                return;
            }

            const file = fileInput.files[0];
            selectedFile = file;

            fileStatus.textContent = '⏳ 正在验证文件...';
            fileStatus.style.color = 'orange';
            fileStatus.style.backgroundColor = '#fff3cd';

            try {
                // 读取文件内容
                fileContent = await file.text();

                // 验证JSON格式
                let jsonData;
                try {
                    jsonData = JSON.parse(fileContent);
                } catch (e) {
                    throw new Error('文件不是有效的JSON格式');
                }

                // 验证必要字段
                const requiredFields = ['client_email', 'token_uri', 'private_key'];
                const missingFields = requiredFields.filter(field => !jsonData[field]);

                if (missingFields.length > 0) {
                    throw new Error(`缺少必要字段: ${missingFields.join(', ')}`);
                }

                // 验证成功
                isFileValid = true;
                fileStatus.textContent = '✅ 文件验证成功';
                fileStatus.style.color = 'green';
                fileStatus.style.backgroundColor = '#d4edda';

                const fileName = file.name || '未知文件';
                const fileSize = file.size ? (file.size / 1024).toFixed(1) : '0';
                const clientEmail = jsonData.client_email || '未知';
                fileDetails.textContent = `文件: ${fileName} (${fileSize} KB) | 客户端: ${clientEmail}`;

                updateButtonStates();
                logOutput('📁 Google API文件选择成功: ' + file.name);
                logOutput('📧 客户端邮箱: ' + jsonData.client_email);

            } catch (error) {
                isFileValid = false;
                fileStatus.textContent = '❌ ' + error.message;
                fileStatus.style.color = 'red';
                fileStatus.style.backgroundColor = '#f8d7da';
                fileDetails.textContent = '';
                updateButtonStates();
                logOutput('❌ 文件验证失败: ' + error.message);
            }
        }

        function updateButtonStates() {
            const jwtBtn = document.getElementById('jwt_btn');
            const orderBtn = document.getElementById('order_btn');
            const consumeBtn = document.getElementById('consume_btn');
            const detailsBtn = document.getElementById('details_btn');

            // JWT按钮：需要有效文件
            jwtBtn.disabled = !isFileValid;

            // API操作按钮：需要有效文件
            orderBtn.disabled = !isFileValid;

            // 消费和详情按钮：需要先执行获取订单信息
            // 这些按钮的状态会在getOrderInfo成功后更新
        }

        function checkFileBeforeOperation(operationName) {
            if (!selectedFile) {
                alert('请先选择Google API JSON文件');
                logOutput('❌ ' + operationName + '失败: 未选择文件');
                return false;
            }

            if (!isFileValid) {
                alert('选择的文件无效，请选择有效的Google API JSON文件');
                logOutput('❌ ' + operationName + '失败: 文件无效');
                return false;
            }

            return true;
        }

        async function generateJWT() {
            if (!checkFileBeforeOperation('JWT Token生成')) {
                return;
            }

            try {
                const result = await makeRequest('/api/generate_jwt', {
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    logOutput('✅ JWT Token 生成成功');
                    if (result.token) {
                        const tokenDisplay = result.token.length > 50 ?
                            result.token.substring(0, 50) + '...' :
                            result.token;
                        logOutput('🔑 Token: ' + tokenDisplay);
                    }
                    if (result.expiry_time) {
                        logOutput('⏰ 过期时间: ' + result.expiry_time);
                    }
                    if (result.client_email) {
                        logOutput('📧 客户端邮箱: ' + result.client_email);
                    }
                } else {
                    logOutput('❌ JWT Token 生成失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                logOutput('❌ JWT Token 生成错误: ' + error.message);
            }
        }

        // API调用函数
        async function getOrderInfo() {
            if (!checkFileBeforeOperation('获取订单信息')) {
                return;
            }

            const packageName = document.getElementById('package_name').value.trim();
            const orderId = document.getElementById('order_id').value.trim();

            if (!packageName || !orderId) {
                alert('请输入包名和订单ID');
                return;
            }

            try {
                const result = await makeRequest('/api/get_order', {
                    package_name: packageName,
                    order_id: orderId,
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    document.getElementById('consume_btn').disabled = false;
                    document.getElementById('details_btn').disabled = false;
                    logOutput('✅ 订单信息获取成功');
                    saveData(); // 保存输入的数据
                } else {
                    logOutput('❌ 获取订单信息失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ 获取订单信息错误: ' + error.message);
            }
        }
        
        async function consumeProduct() {
            if (!checkFileBeforeOperation('产品消费')) {
                return;
            }

            try {
                const result = await makeRequest('/api/consume', {
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    logOutput('✅ 产品消费成功');
                } else {
                    logOutput('❌ 产品消费失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ 产品消费错误: ' + error.message);
            }
        }

        async function productDetails() {
            if (!checkFileBeforeOperation('获取产品详情')) {
                return;
            }

            try {
                const result = await makeRequest('/api/details', {
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    logOutput('✅ 产品详情获取成功');
                } else {
                    logOutput('❌ 获取产品详情失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ 获取产品详情错误: ' + error.message);
            }
        }
        
        async function runAllSteps() {
            if (!checkFileBeforeOperation('执行完整工作流程')) {
                return;
            }

            const packageName = document.getElementById('package_name').value.trim();
            const orderId = document.getElementById('order_id').value.trim();

            if (!packageName || !orderId) {
                alert('请输入包名和订单ID');
                return;
            }

            logOutput('🚀 开始执行完整工作流程...');
            logOutput('═'.repeat(50));

            try {
                // Step 1
                logOutput('📋 步骤 1/3: 获取订单信息');
                await getOrderInfo();

                // Step 2
                await new Promise(resolve => setTimeout(resolve, 1000));
                logOutput('🛒 步骤 2/3: 消费产品');
                await consumeProduct();

                // Step 3
                await new Promise(resolve => setTimeout(resolve, 1000));
                logOutput('📊 步骤 3/3: 获取产品详情');
                await productDetails();

                logOutput('═'.repeat(50));
                logOutput('🎉 工作流程完成!');
            } catch (error) {
                logOutput('❌ 工作流程执行错误: ' + error.message);
            }
        }
        
        // 页面初始化
        window.onload = async function() {
            // 加载保存的数据
            const saved = localStorage.getItem('api_tool_data');
            if (saved) {
                const data = JSON.parse(saved);
                document.getElementById('package_name').value = data.package_name || '';
                document.getElementById('order_id').value = data.order_id || '';
            }

            // 初始化按钮状态
            updateButtonStates();

            // 显示欢迎信息
            logOutput('🌟 Google Play Developer API Tool 已启动');
            logOutput('📝 请选择Google API JSON文件，然后输入包名和订单ID');
            logOutput('💡 操作流程:');
            logOutput('   1. 选择Google API JSON文件');
            logOutput('   2. 输入包名和订单ID');
            logOutput('   3. 生成JWT Token（可选）');
            logOutput('   4. 执行API操作');
            logOutput('═'.repeat(50));

            // 绑定输入事件
            document.getElementById('package_name').addEventListener('input', saveData);
            document.getElementById('order_id').addEventListener('input', saveData);
        };
    </script>
</body>
</html>'''
            self.wfile.write(html_content.encode())
            
        elif self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)

    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def handle_api_request(self):
        try:
            # 获取请求数据
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                try:
                    data = json.loads(post_data.decode('utf-8'))
                except json.JSONDecodeError as e:
                    self.send_json_response(400, {'success': False, 'error': f'JSON解析错误: {str(e)}'})
                    return
            else:
                data = {}

            response = {'success': False, 'error': 'Unknown endpoint', 'logs': []}

            if self.path == '/api/get_order':
                response = self.execute_api_call(
                    lambda: self.handle_get_order(data),
                    "获取订单信息"
                )

            elif self.path == '/api/consume':
                response = self.execute_api_call(
                    lambda: self.handle_consume(data),
                    "消费产品"
                )

            elif self.path == '/api/details':
                response = self.execute_api_call(
                    lambda: self.handle_details(data),
                    "获取产品详情"
                )

            elif self.path == '/api/upload_file':
                response = self.handle_file_upload(data)

            elif self.path == '/api/file_info':
                response = self.handle_file_info()

            elif self.path == '/api/generate_jwt':
                response = self.execute_api_call(
                    lambda: self.handle_generate_jwt(data),
                    "生成JWT Token"
                )

            self.send_json_response(200, response)

        except Exception as e:
            print(f"API请求处理错误: {str(e)}")  # 服务器端日志
            import traceback
            traceback.print_exc()

            error_response = {'success': False, 'error': f'服务器内部错误: {str(e)}', 'logs': []}
            self.send_json_response(500, error_response)

    def send_json_response(self, status_code, data):
        """发送JSON响应"""
        try:
            self.send_response(status_code)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()

            json_data = json.dumps(data, ensure_ascii=False, default=str)
            self.wfile.write(json_data.encode('utf-8'))
        except Exception as e:
            print(f"发送JSON响应错误: {str(e)}")
            # 发送最基本的错误响应
            try:
                self.send_response(500)
                self.send_header('Content-type', 'application/json; charset=utf-8')
                self.end_headers()
                basic_error = '{"success": false, "error": "Response encoding error"}'
                self.wfile.write(basic_error.encode('utf-8'))
            except:
                pass

    def execute_api_call(self, func, operation_name):
        """执行API调用并捕获日志"""
        # 清空之前的日志
        with log_lock:
            captured_logs.clear()

        add_log(f"🚀 开始{operation_name}...")
        start_time = datetime.now()

        old_stdout = sys.stdout
        old_stderr = sys.stderr

        try:
            # 重定向输出到日志捕获器
            sys.stdout = log_capture
            sys.stderr = log_capture

            # 执行函数
            result = func()

            # 添加成功日志
            elapsed = (datetime.now() - start_time).total_seconds()
            add_log(f"✅ {operation_name}完成 (耗时: {elapsed:.2f}秒)")

            with log_lock:
                return {
                    'success': True,
                    'logs': captured_logs.copy(),
                    'result': result if result is not None else {}
                }

        except Exception as e:
            # 添加错误日志
            elapsed = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            add_log(f"❌ {operation_name}失败: {error_msg} (耗时: {elapsed:.2f}秒)")

            # 打印详细错误到服务器控制台
            print(f"API调用错误 - {operation_name}: {error_msg}")
            import traceback
            traceback.print_exc()

            with log_lock:
                return {
                    'success': False,
                    'error': error_msg,
                    'logs': captured_logs.copy()
                }

        finally:
            # 确保输出总是被恢复
            sys.stdout = old_stdout
            sys.stderr = old_stderr

    def handle_get_order(self, data):
        """处理获取订单请求"""
        package_name = data.get('package_name', '').strip()
        order_id = data.get('order_id', '').strip()
        file_content = data.get('file_content')
        filename = data.get('filename', 'googleapi.json')

        if not package_name or not order_id:
            raise ValueError("包名和订单ID不能为空")

        if not file_content:
            raise ValueError("请提供Google API JSON文件内容")

        add_log(f"📱 包名: {package_name}")
        add_log(f"🆔 订单ID: {order_id}")

        # 处理文件内容
        success, result_path = file_selector.handle_file_upload(file_content, filename)
        if not success:
            raise ValueError(f"文件处理失败: {result_path}")

        # 设置API使用的文件
        from apis import set_google_api_file
        set_google_api_file(result_path)

        body = {
            "package_name": package_name,
            "order_id": order_id
        }

        get_order(body)
        return {"package_name": package_name, "order_id": order_id}

    def handle_consume(self, data):
        """处理消费产品请求"""
        file_content = data.get('file_content')
        filename = data.get('filename', 'googleapi.json')

        if not file_content:
            raise ValueError("请提供Google API JSON文件内容")

        # 处理文件内容
        success, result_path = file_selector.handle_file_upload(file_content, filename)
        if not success:
            raise ValueError(f"文件处理失败: {result_path}")

        # 设置API使用的文件
        from apis import set_google_api_file
        set_google_api_file(result_path)

        product_consume()
        return {}

    def handle_details(self, data):
        """处理获取产品详情请求"""
        file_content = data.get('file_content')
        filename = data.get('filename', 'googleapi.json')

        if not file_content:
            raise ValueError("请提供Google API JSON文件内容")

        # 处理文件内容
        success, result_path = file_selector.handle_file_upload(file_content, filename)
        if not success:
            raise ValueError(f"文件处理失败: {result_path}")

        # 设置API使用的文件
        from apis import set_google_api_file
        set_google_api_file(result_path)

        product_details()
        return {}

    def handle_file_upload(self, data):
        """处理文件上传"""
        try:
            file_content = data.get('file_content', '')
            filename = data.get('filename', 'googleapi.json')

            if not file_content:
                return {'success': False, 'error': '文件内容为空'}

            success, result = file_selector.handle_file_upload(file_content, filename)

            if success:
                return {
                    'success': True,
                    'message': '文件上传成功',
                    'file_path': result
                }
            else:
                return {'success': False, 'error': result}

        except Exception as e:
            return {'success': False, 'error': f'上传文件时发生错误: {str(e)}'}

    def handle_file_info(self):
        """获取当前文件信息"""
        try:
            file_info = file_selector.get_file_info()
            return {'success': True, 'file_info': file_info}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def handle_generate_jwt(self, data):
        """生成JWT Token"""
        try:
            scope = data.get('scope', 'https://www.googleapis.com/auth/androidpublisher')
            hours = data.get('hours', 1)
            file_content = data.get('file_content')
            filename = data.get('filename', 'googleapi.json')

            if not file_content:
                raise ValueError("请提供Google API JSON文件内容")

            # 处理文件内容
            success, result_path = file_selector.handle_file_upload(file_content, filename)
            if not success:
                raise ValueError(f"文件处理失败: {result_path}")

            # 加载配置并生成token
            jwt_generator.load_google_api_config(result_path)
            result = jwt_generator.generate_jwt_token(scope, hours)

            add_log(f"JWT Token生成成功")
            add_log(f"客户端邮箱: {result['client_email']}")
            add_log(f"过期时间: {result['expiry_time']}")

            return result

        except Exception as e:
            raise Exception(f"生成JWT Token失败: {str(e)}")

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            return result != 0
    except Exception:
        return False

def find_process_using_port(port):
    """查找占用端口的进程"""
    try:
        system = platform.system().lower()
        if system == "windows":
            # Windows系统
            cmd = f'netstat -ano | findstr :{port}'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            # 获取进程名
                            cmd_name = f'tasklist /FI "PID eq {pid}" /FO CSV /NH'
                            name_result = subprocess.run(cmd_name, shell=True, capture_output=True, text=True)
                            if name_result.returncode == 0 and name_result.stdout.strip():
                                process_name = name_result.stdout.strip().split(',')[0].strip('"')
                                return pid, process_name
        else:
            # macOS/Linux系统
            cmd = f'lsof -ti:{port}'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                pid = result.stdout.strip().split('\n')[0]
                # 获取进程名
                cmd_name = f'ps -p {pid} -o comm='
                name_result = subprocess.run(cmd_name, shell=True, capture_output=True, text=True)
                if name_result.returncode == 0 and name_result.stdout.strip():
                    process_name = name_result.stdout.strip()
                    return pid, process_name
                return pid, "Unknown"
    except Exception as e:
        print(f"查找进程时出错: {e}")
    return None, None

def kill_process_by_pid(pid):
    """根据PID结束进程"""
    try:
        system = platform.system().lower()
        if system == "windows":
            cmd = f'taskkill /F /PID {pid}'
        else:
            cmd = f'kill -9 {pid}'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"结束进程时出错: {e}")
        return False

def find_available_port(start_port=8080, max_attempts=20):
    """寻找可用端口"""
    print(f"🔍 正在寻找可用端口 (从 {start_port} 开始)...")
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            print(f"✅ 端口 {port} 可用")
            return port
        else:
            print(f"❌ 端口 {port} 被占用")

    # 如果常规端口都被占用，尝试更高的端口范围
    print("🔍 尝试更高端口范围...")
    for port in range(9000, 9020):
        if check_port_available(port):
            print(f"✅ 端口 {port} 可用")
            return port
        else:
            print(f"❌ 端口 {port} 被占用")

    return None

def handle_port_conflict(port, auto_mode=False):
    """处理端口冲突"""
    print(f"\n⚠️  端口 {port} 已被占用!")

    # 查找占用进程
    pid, process_name = find_process_using_port(port)

    if pid:
        print(f"📋 占用进程: {process_name} (PID: {pid})")

        # 自动模式：直接使用其他端口
        if auto_mode:
            print("🔄 自动模式：寻找其他可用端口...")
            available_port = find_available_port()
            if available_port:
                print(f"✅ 找到可用端口: {available_port}")
                return available_port
            else:
                print("❌ 未找到可用端口")
                return None

        # 交互模式
        print("\n请选择处理方式:")
        print("1. 结束占用进程并使用端口 8080")
        print("2. 使用其他可用端口")
        print("3. 退出程序")

        # 检查是否在交互式环境中
        try:
            # 尝试检测是否有可用的标准输入
            import select
            import sys
            if hasattr(select, 'select'):
                # Unix系统
                if not select.select([sys.stdin], [], [], 0.1)[0]:
                    print("🔄 检测到非交互式环境，自动使用其他端口...")
                    available_port = find_available_port()
                    if available_port:
                        print(f"✅ 找到可用端口: {available_port}")
                        return available_port
                    else:
                        print("❌ 未找到可用端口")
                        return None
        except:
            # Windows系统或其他情况，尝试交互式输入
            pass

        # 添加超时机制的输入
        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            try:
                choice = input(f"\n请输入选择 (1/2/3) [尝试 {attempt + 1}/{max_attempts}]: ").strip()

                if choice == "1":
                    print(f"🔄 正在结束进程 {process_name} (PID: {pid})...")
                    if kill_process_by_pid(pid):
                        print("✅ 进程已结束")
                        time.sleep(1)  # 等待端口释放
                        if check_port_available(port):
                            return port
                        else:
                            print("❌ 端口仍被占用，可能需要等待更长时间")
                            return find_available_port()
                    else:
                        print("❌ 无法结束进程，可能需要管理员权限")
                        return find_available_port()

                elif choice == "2":
                    available_port = find_available_port()
                    if available_port:
                        print(f"✅ 找到可用端口: {available_port}")
                        return available_port
                    else:
                        print("❌ 未找到可用端口")
                        return None

                elif choice == "3":
                    print("👋 程序退出")
                    return None

                else:
                    print("❌ 无效选择，请输入 1、2 或 3")
                    attempt += 1
                    continue

            except KeyboardInterrupt:
                print("\n👋 程序退出")
                return None
            except EOFError:
                print("\n🔄 检测到输入结束，自动使用其他端口...")
                available_port = find_available_port()
                if available_port:
                    print(f"✅ 找到可用端口: {available_port}")
                    return available_port
                else:
                    print("❌ 未找到可用端口")
                    return None
            except Exception as e:
                print(f"❌ 输入处理错误: {e}")
                attempt += 1
                if attempt >= max_attempts:
                    print("🔄 达到最大尝试次数，自动使用其他端口...")
                    available_port = find_available_port()
                    if available_port:
                        print(f"✅ 找到可用端口: {available_port}")
                        return available_port
                    else:
                        print("❌ 未找到可用端口")
                        return None

        # 如果所有尝试都失败，自动使用其他端口
        print("🔄 输入超时，自动使用其他端口...")
        available_port = find_available_port()
        if available_port:
            print(f"✅ 找到可用端口: {available_port}")
            return available_port
        else:
            print("❌ 未找到可用端口")
            return None
    else:
        print("❌ 无法确定占用进程")
        available_port = find_available_port()
        if available_port:
            print(f"✅ 自动使用可用端口: {available_port}")
            return available_port
        else:
            print("❌ 未找到可用端口")
            return None

def start_server(auto_mode=False):
    """启动服务器，处理端口冲突

    Args:
        auto_mode (bool): 是否使用自动模式（不需要用户交互）
    """
    DEFAULT_PORT = 8080
    Handler = APIHandler

    print("🚀 启动 Google Play Developer API Tool...")

    # 检查默认端口是否可用
    if check_port_available(DEFAULT_PORT):
        port = DEFAULT_PORT
        print(f"✅ 端口 {port} 可用")
    else:
        # 处理端口冲突
        port = handle_port_conflict(DEFAULT_PORT, auto_mode)
        if port is None:
            print("❌ 无法启动服务器")
            return

    # 尝试启动服务器，如果失败则重试其他端口
    max_retries = 3
    for attempt in range(max_retries):
        try:
            with socketserver.TCPServer(("", port), Handler) as httpd:
                print(f"🌐 服务器运行在: http://localhost:{port}")
                print("🔗 正在打开浏览器...")

                # Open browser after a short delay
                def open_browser():
                    time.sleep(1)
                    webbrowser.open(f'http://localhost:{port}')

                threading.Thread(target=open_browser, daemon=True).start()

                print("📝 按 Ctrl+C 停止服务器")
                print("=" * 50)

                try:
                    httpd.serve_forever()
                except KeyboardInterrupt:
                    print("\n🛑 服务器已停止")
                return  # 成功启动，退出函数

        except Exception as e:
            print(f"❌ 启动服务器失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if "Address already in use" in str(e) or "Only one usage" in str(e):
                if attempt < max_retries - 1:
                    print("🔄 端口可能在启动过程中被占用，尝试寻找新端口...")
                    new_port = find_available_port(port + 1)
                    if new_port:
                        port = new_port
                        print(f"🔄 重试使用端口 {port}...")
                        time.sleep(1)  # 等待一秒再重试
                        continue
                    else:
                        print("❌ 无法找到可用端口")
                        break
                else:
                    print("💡 提示: 所有尝试都失败，请检查系统端口使用情况或重启计算机")
            else:
                print(f"💡 提示: 服务器启动遇到其他问题: {e}")
                break

    print("❌ 无法启动服务器")

if __name__ == "__main__":
    # 检查命令行参数
    import sys
    auto_mode = "--auto" in sys.argv or "-a" in sys.argv
    if auto_mode:
        print("🤖 自动模式已启用")
    start_server(auto_mode)
