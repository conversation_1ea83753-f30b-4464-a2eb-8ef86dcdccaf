#!/usr/bin/env python3
"""
Tkinter GUI for Google Play Developer API Tool
使用解耦的文件选择器和JWT生成器
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime

from file_selector import TkinterFileSelector
from jwt_generator import JWTGenerator
from apis import get_order, product_consume, product_details, set_jwt_generator

class GooglePlayAPIGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Google Play Developer API Tool")
        self.root.geometry("800x600")

        # 初始化组件
        self.file_selector = TkinterFileSelector()
        self.jwt_generator = JWTGenerator()
        self.current_file_path = None

        # 设置API使用的JWT生成器
        set_jwt_generator(self.jwt_generator)

        self.setup_ui()
        self.check_existing_file()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="Google API 文件", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=1)
        ttk.Button(file_frame, text="生成JWT Token", command=self.generate_jwt).grid(row=0, column=2, padx=(5, 0))

        # API参数区域
        params_frame = ttk.LabelFrame(main_frame, text="API 参数", padding="5")
        params_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(params_frame, text="Package Name:").grid(row=0, column=0, sticky=tk.W)
        self.package_entry = ttk.Entry(params_frame, width=40)
        self.package_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))

        ttk.Label(params_frame, text="Order ID:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.order_entry = ttk.Entry(params_frame, width=40)
        self.order_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(5, 0))

        # 操作按钮区域
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, columnspan=2, pady=(0, 10))

        ttk.Button(buttons_frame, text="1. Get Order Info", command=self.get_order_info).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(buttons_frame, text="2. Consume Product", command=self.consume_product).grid(row=0, column=1, padx=5)
        ttk.Button(buttons_frame, text="3. Product Details", command=self.product_details).grid(row=0, column=2, padx=(5, 0))

        ttk.Button(buttons_frame, text="Run All Steps", command=self.run_all_steps).grid(row=1, column=0, columnspan=3, pady=(10, 0))

        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="输出", padding="5")
        output_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Button(output_frame, text="清除输出", command=self.clear_output).grid(row=1, column=0, pady=(5, 0))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        params_frame.columnconfigure(1, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)

    def log_output(self, message):
        """输出日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.output_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.output_text.see(tk.END)
        self.root.update_idletasks()

    def clear_output(self):
        """清除输出"""
        self.output_text.delete(1.0, tk.END)

    def check_existing_file(self):
        """检查是否有已存在的文件"""
        last_file = self.jwt_generator.get_last_file_path()
        if last_file:
            import os
            if os.path.exists(last_file):
                self.current_file_path = last_file
                self.file_label.config(text=f"当前文件: {os.path.basename(last_file)}")
                self.log_output(f"检测到已有文件: {os.path.basename(last_file)}")

    def select_file(self):
        """选择文件"""
        try:
            file_path = self.file_selector.select_file_with_last_option()
            if file_path:
                is_valid, message = self.file_selector.validate_file(file_path)
                if is_valid:
                    self.current_file_path = file_path
                    import os
                    self.file_label.config(text=f"当前文件: {os.path.basename(file_path)}")
                    self.log_output(f"文件选择成功: {os.path.basename(file_path)}")
                else:
                    messagebox.showerror("错误", f"文件无效: {message}")
                    self.log_output(f"文件无效: {message}")
        except Exception as e:
            messagebox.showerror("错误", f"选择文件时发生错误: {str(e)}")
            self.log_output(f"选择文件错误: {str(e)}")

    def generate_jwt(self):
        """生成JWT Token"""
        if not self.current_file_path:
            messagebox.showwarning("警告", "请先选择Google API JSON文件")
            return

        try:
            self.log_output("正在生成JWT Token...")
            self.jwt_generator.load_google_api_config(self.current_file_path)
            result = self.jwt_generator.generate_jwt_token()

            self.log_output("✅ JWT Token 生成成功")
            self.log_output(f"🔑 Token: {result['token']}")
            self.log_output(f"⏰ 过期时间: {result['expiry_time']}")
            self.log_output(f"📧 客户端邮箱: {result['client_email']}")

            messagebox.showinfo("成功", "JWT Token 生成成功！")

        except Exception as e:
            error_msg = f"生成JWT Token失败: {str(e)}"
            self.log_output(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def run_api_operation(self, operation_func, operation_name):
        """在后台线程中运行API操作"""
        def run():
            try:
                self.log_output(f"🚀 开始{operation_name}...")
                operation_func()
                self.log_output(f"✅ {operation_name}完成")
            except Exception as e:
                error_msg = f"{operation_name}失败: {str(e)}"
                self.log_output(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)

        threading.Thread(target=run, daemon=True).start()

    def get_order_info(self):
        """获取订单信息"""
        package_name = self.package_entry.get().strip()
        order_id = self.order_entry.get().strip()

        if not package_name or not order_id:
            messagebox.showwarning("警告", "请输入包名和订单ID")
            return

        def operation():
            body = {
                "package_name": package_name,
                "order_id": order_id
            }
            get_order(body)

        self.run_api_operation(operation, "获取订单信息")

    def consume_product(self):
        """消费产品"""
        self.run_api_operation(product_consume, "消费产品")

    def product_details(self):
        """获取产品详情"""
        self.run_api_operation(product_details, "获取产品详情")

    def run_all_steps(self):
        """运行所有步骤"""
        package_name = self.package_entry.get().strip()
        order_id = self.order_entry.get().strip()

        if not package_name or not order_id:
            messagebox.showwarning("警告", "请输入包名和订单ID")
            return

        def run_all():
            try:
                self.log_output("🚀 开始执行完整工作流程...")
                self.log_output("═" * 50)

                # Step 1
                self.log_output("📋 步骤 1/3: 获取订单信息")
                body = {"package_name": package_name, "order_id": order_id}
                get_order(body)
                self.log_output("✅ 步骤 1 完成")

                # Step 2
                self.log_output("🛒 步骤 2/3: 消费产品")
                product_consume()
                self.log_output("✅ 步骤 2 完成")

                # Step 3
                self.log_output("📊 步骤 3/3: 获取产品详情")
                product_details()
                self.log_output("✅ 步骤 3 完成")

                self.log_output("═" * 50)
                self.log_output("🎉 工作流程完成!")

                messagebox.showinfo("成功", "所有步骤执行完成！")

            except Exception as e:
                error_msg = f"执行工作流程失败: {str(e)}"
                self.log_output(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)

        threading.Thread(target=run_all, daemon=True).start()

def main():
    """主函数"""
    root = tk.Tk()
    app = GooglePlayAPIGUI(root)

    # 显示欢迎信息
    app.log_output("🌟 Google Play Developer API Tool 已启动")
    app.log_output("📝 请选择Google API JSON文件，然后输入包名和订单ID")
    app.log_output("💡 提示: 先生成JWT Token，然后执行API操作")
    app.log_output("═" * 50)

    root.mainloop()

if __name__ == "__main__":
    main()