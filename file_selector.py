"""
文件选择器模块 - 为不同UI框架提供文件选择功能
支持 tkinter 和 web 界面
"""

import os
import tkinter as tk
from tkinter import filedialog, messagebox
from jwt_generator import JWTGenerator

class FileSelector:
    """文件选择器基类"""
    
    def __init__(self):
        self.jwt_generator = JWTGenerator()
    
    def get_last_file_path(self):
        """获取上次使用的文件路径"""
        return self.jwt_generator.get_last_file_path()
    
    def get_last_directory(self):
        """获取上次使用的目录"""
        return self.jwt_generator.get_last_directory()
    
    def validate_file(self, file_path):
        """验证文件是否有效"""
        if not file_path:
            return False, "未选择文件"
        
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}"
        
        if not file_path.lower().endswith('.json'):
            return False, "请选择JSON文件"
        
        try:
            # 尝试加载文件验证格式
            self.jwt_generator.load_google_api_config(file_path)
            return True, "文件验证成功"
        except Exception as e:
            return False, str(e)

class TkinterFileSelector(FileSelector):
    """Tkinter文件选择器"""
    
    def select_file(self, title="选择Google API JSON文件"):
        """使用tkinter选择文件"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 获取初始目录
        initial_dir = self.get_last_directory()
        
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title=title,
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir=initial_dir
        )
        
        root.destroy()
        return file_path
    
    def show_message(self, title, message, msg_type="info"):
        """显示消息框"""
        root = tk.Tk()
        root.withdraw()
        
        if msg_type == "error":
            messagebox.showerror(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        else:
            messagebox.showinfo(title, message)
        
        root.destroy()
    
    def ask_yes_no(self, title, message):
        """询问是否确认"""
        root = tk.Tk()
        root.withdraw()
        
        result = messagebox.askyesno(title, message)
        root.destroy()
        return result
    
    def select_file_with_last_option(self):
        """选择文件，如果有上次的文件则询问是否使用"""
        last_file = self.get_last_file_path()
        
        if last_file and os.path.exists(last_file):
            use_last = self.ask_yes_no(
                "使用上次的文件",
                f"是否使用上次选择的文件？\n{last_file}"
            )
            
            if use_last:
                return last_file
        
        return self.select_file()

class WebFileSelector(FileSelector):
    """Web界面文件选择器 - 提供文件路径处理功能"""
    
    def __init__(self):
        super().__init__()
        self.uploaded_files = {}  # 存储上传的文件内容
    
    def handle_file_upload(self, file_content, filename):
        """处理web上传的文件内容"""
        if not filename.lower().endswith('.json'):
            return False, "请上传JSON文件"
        
        try:
            import json
            # 验证JSON格式
            file_data = json.loads(file_content)
            
            # 验证必要字段
            required_fields = ["client_email", "token_uri", "private_key"]
            for field in required_fields:
                if field not in file_data:
                    return False, f"JSON文件缺少必要字段: {field}"
            
            # 保存文件内容到临时位置
            temp_file_path = f"temp_{filename}"
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            # 保存到配置
            self.jwt_generator.save_file_path(temp_file_path)
            
            return True, temp_file_path
            
        except json.JSONDecodeError as e:
            return False, f"JSON格式错误: {str(e)}"
        except Exception as e:
            return False, f"处理文件时发生错误: {str(e)}"
    
    def get_file_info(self):
        """获取当前文件信息"""
        last_file = self.get_last_file_path()
        if last_file and os.path.exists(last_file):
            return {
                "has_file": True,
                "file_path": last_file,
                "file_name": os.path.basename(last_file)
            }
        return {"has_file": False}

# 便捷函数
def select_file_tkinter():
    """使用tkinter选择文件的便捷函数"""
    selector = TkinterFileSelector()
    return selector.select_file_with_last_option()

def validate_google_api_file(file_path):
    """验证Google API文件的便捷函数"""
    selector = FileSelector()
    return selector.validate_file(file_path)

# 示例用法
if __name__ == "__main__":
    # 测试tkinter选择器
    print("测试文件选择器...")
    
    selector = TkinterFileSelector()
    file_path = selector.select_file_with_last_option()
    
    if file_path:
        is_valid, message = selector.validate_file(file_path)
        if is_valid:
            print(f"选择的文件有效: {file_path}")
            selector.show_message("成功", f"文件选择成功!\n{file_path}")
        else:
            print(f"文件无效: {message}")
            selector.show_message("错误", f"文件无效: {message}", "error")
    else:
        print("未选择文件")
