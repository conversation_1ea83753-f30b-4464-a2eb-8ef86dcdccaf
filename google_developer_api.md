# 怎么使用 google developer api

## 生成 jwt
1. [jwt.io](https://jwt.io/)
2. Header
``` Json
{
    "alg": "RS256",
    "typ": "JWT"
}    
```
3. Payload
    - 时间戳都是秒格式
    - `exp`: 当前时间戳 + 1小时
    - `iat`: 当前时间戳
``` J<PERSON>
{
    "iss": "***",
    "scope": "https://www.googleapis.com/auth/androidpublisher",
    "aud": "https://oauth2.googleapis.com/token",
    "exp": 1753097723,
    "iat": 1753094123
}
```

4. Sign JWT: Private Key
``` Plain Text
-----BEGIN PRIVATE KEY-----
***
-----END PRIVATE KEY-----
```
   
## 获取token
1. [api地址](https://oauth2.googleapis.com/token) `POST`
2. `header`: `Content-Type: application/x-www-form-urlencoded`
3. `body`:
   1. `grant_type`: `urn:ietf:params:oauth:grant-type:jwt-bearer`
   2. `assertion`: 使用生成的JWT
4. `response`: 
    ``` Json
   {
        "access_token": "xxxxxxx",
        "expires_in": 3599,
        "token_type": "Bearer"
    }
    ```

## 消耗订单
1. [api地址](https://androidpublisher.googleapis.com/androidpublisher/v3/applications/:packageName/purchases/products/:productId/tokens/:token:consume) `GET`
2. `params`: `query`
   1. `packageName`: `com.tf.localuv`
   2. `productId`: `prod_id`
   3. `token`: `purchase_token`
3. `Authorization`: `Bearer Token`
   1. 获取token拿到的token